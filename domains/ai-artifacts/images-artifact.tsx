import { useLocale } from '@bika/contents/i18n';
import type { FileArtifactFileType } from '@bika/types/ai/vo';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { IconButton } from '@bika/ui/button-component';
import DownloadOutlined from '@bika/ui/icons/components/download_outlined';
import { directDownload } from '@bika/ui/preview-attachment/attachment-renderer';
import { FilePreview } from '@bika/ui/preview-attachment/file-preview';
import type { ToolUIPart } from 'ai';
import React from 'react';
import { ArtifactContainerWithModal } from '../ai/client/chat/artifacts/components/artifact-container-with-modal';

//   type: 'image',
//   data: image.fullUrl,
//   mimeType: vo.mimeType,
interface GeneratedImage {
  type: 'image';
  date: string;
  mimeType: string;
}
interface FileArtifactProps {
  dataList: GeneratedImage[];
  //   filePath?: string;
  content?: string;
  //   fileType?: FileArtifactFileType;
  skillsets?: SkillsetSelectDTO[];
  tool?: ToolUIPart;
  expandable?: boolean;
}

export const ImagesArtifact = (props: FileArtifactProps) => {
  const { content, dataList, skillsets = [], tool, expandable = true } = props;
  const { t } = useLocale();

  //   const data = { filePath, content };

  const downloadButton = (
    <>
      <IconButton
        variant="plain"
        size="sm"
        color="neutral"
        onClick={() => {
          console.log('noo');
          //   if (filePath) {
          //     // Extract filename from filePath or use a default name
          //     const filename = filePath.split('/').pop() || 'download';
          //     await directDownload(filePath, filename);
          //   }
        }}
        sx={{
          '&:hover': {
            backgroundColor: 'var(--hover)',
          },
        }}
      >
        <DownloadOutlined color="var(--text-primary)" />
      </IconButton>
    </>
  );

  return (
    <ArtifactContainerWithModal
      data={data}
      skillsets={skillsets}
      tool={tool}
      modalProps={{
        width: '100vw',
        height: '100vh',
      }}
      expandable={expandable}
      rowDataType="json"
      toolbarButton={downloadButton}
      switchProps={{
        previewLabel: t.ai.artifact_preview,
      }}
    >

{
    dataList.map((_item, index) => {

            const attach = (_values as AttachmentVO[])[index];
            const fileType = isWhatFileType({ name: attach.name, type: attach.mimeType });

            const links = (v as any).links;
            return (
              <div
                key={index}
                className={'relative h-[22px] w-[22px] flex-shrink-0 cursor-pointer'}
                onClick={() => handlePreviewAttachment(index)}
              >
                <Tooltip title={attach.name} placement="top">
                  <img
                    alt=""
                    // fill={true}
                    loading="lazy"
                    src={
                      fileType === FileType.Image
                        ? links?.thumbnailUrl
                        : renderFileIconUrl({ name: attach.name, type: attach.mimeType })
                    }
                    // objectFit="cover"
                    className={'border-[1px] border-[--border-default] h-[22px] w-[22px]'}
                  />
                </Tooltip>
              </div>
    }
    </>))
}
      {/* {filePath && <FilePreview url={filePath} />} */}
    </ArtifactContainerWithModal>
  );
};
